"""
Пагинатор для уроков на основе aiogramx
"""
from typing import List, Optional, Callable, Awaitable
from aiogram.types import InlineKeyboardButton, CallbackQuery, InlineKeyboardMarkup
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogramx import Paginator
from database import LessonRepository
from common.keyboards import get_main_menu_back_button


class CustomLessonsPaginator(Paginator):
    """Кастомный пагинатор с дополнительными кнопками"""

    async def render_kb(self, page: int = 1) -> InlineKeyboardMarkup:
        """Переопределенный метод рендеринга клавиатуры с дополнительными кнопками"""
        builder = InlineKeyboardBuilder()

        # Добавляем элементы страницы
        await self._get_page_items(builder, page)

        # Добавляем кнопки пагинации
        await self._build_pagination_buttons(builder, page)

        # Добавляем кнопки главного меню
        main_menu_buttons = get_main_menu_back_button()
        for button_row in main_menu_buttons:
            builder.row(*button_row)

        return builder.as_markup()


class LessonsPaginator:
    """Пагинатор для отображения уроков с использованием aiogramx"""
    
    @staticmethod
    async def create_lessons_paginator(
        subject_id: int,
        course_id: int,
        on_select: Optional[Callable[[CallbackQuery, str], Awaitable[None]]] = None,
        on_back: Optional[Callable[[CallbackQuery], Awaitable[None]]] = None,
        per_page: int = 10,
        per_row: int = 1
    ) -> Paginator:
        """
        Создает пагинатор для уроков
        
        Args:
            subject_id: ID предмета
            course_id: ID курса
            on_select: Callback для выбора урока
            on_back: Callback для кнопки "Назад"
            per_page: Количество уроков на странице
            per_row: Количество кнопок в ряду
            
        Returns:
            Настроенный пагинатор
        """
        # Получаем уроки из базы данных
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
        
        # Создаем кнопки для уроков
        lesson_buttons = []
        for lesson in lessons:
            button = InlineKeyboardButton(
                text=lesson.name,
                callback_data=f"lesson_{lesson.id}"
            )
            lesson_buttons.append(button)
        
        # Создаем кастомный пагинатор
        paginator = CustomLessonsPaginator(
            per_page=per_page,
            per_row=per_row,
            data=lesson_buttons,
            on_select=on_select,
            on_back=on_back,
            lang="ru"
        )
        
        return paginator
    
    @staticmethod
    async def create_lazy_lessons_paginator(
        subject_id: int,
        course_id: int,
        on_select: Optional[Callable[[CallbackQuery, str], Awaitable[None]]] = None,
        on_back: Optional[Callable[[CallbackQuery], Awaitable[None]]] = None,
        per_page: int = 10,
        per_row: int = 1
    ) -> Paginator:
        """
        Создает lazy пагинатор для уроков (для больших объемов данных)
        
        Args:
            subject_id: ID предмета
            course_id: ID курса
            on_select: Callback для выбора урока
            on_back: Callback для кнопки "Назад"
            per_page: Количество уроков на странице
            per_row: Количество кнопок в ряду
            
        Returns:
            Настроенный lazy пагинатор
        """
        
        async def get_lessons_lazy(cur_page: int, per_page: int) -> List[InlineKeyboardButton]:
            """Lazy загрузка уроков для текущей страницы"""
            # Получаем все уроки (в реальном проекте можно добавить LIMIT/OFFSET в репозиторий)
            all_lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
            
            # Вычисляем индексы для текущей страницы
            start_idx = (cur_page - 1) * per_page
            end_idx = start_idx + per_page
            
            # Получаем уроки для текущей страницы
            page_lessons = all_lessons[start_idx:end_idx]
            
            # Создаем кнопки
            buttons = []
            for lesson in page_lessons:
                button = InlineKeyboardButton(
                    text=lesson.name,
                    callback_data=f"lesson_{lesson.id}"
                )
                buttons.append(button)
            
            return buttons
        
        async def get_lessons_count() -> int:
            """Получает общее количество уроков"""
            lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
            return len(lessons)
        
        # Создаем кастомный lazy пагинатор
        paginator = CustomLessonsPaginator(
            per_page=per_page,
            per_row=per_row,
            lazy_data=get_lessons_lazy,
            lazy_count=get_lessons_count,
            on_select=on_select,
            on_back=on_back,
            lang="ru"
        )
        
        return paginator
