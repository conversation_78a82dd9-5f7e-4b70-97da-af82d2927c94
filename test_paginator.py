#!/usr/bin/env python3
"""
Тест пагинатора для уроков
"""
import asyncio
from aiogramx import Paginator
from aiogram.types import InlineKeyboardButton

async def test_paginator():
    """Тестируем создание пагинатора"""
    
    # Создаем тестовые данные
    test_lessons = [
        {"id": 1, "name": "Урок 1: Основы Python"},
        {"id": 2, "name": "Урок 2: Переменные и типы данных"},
        {"id": 3, "name": "Урок 3: Условные операторы"},
        {"id": 4, "name": "Урок 4: Циклы"},
        {"id": 5, "name": "Урок 5: Функции"},
        {"id": 6, "name": "Урок 6: Списки и кортежи"},
        {"id": 7, "name": "Урок 7: Словари и множества"},
        {"id": 8, "name": "Урок 8: Работа с файлами"},
        {"id": 9, "name": "Урок 9: Исключения"},
        {"id": 10, "name": "Урок 10: Классы и объекты"},
        {"id": 11, "name": "Урок 11: Наследование"},
        {"id": 12, "name": "Урок 12: Модули и пакеты"},
        {"id": 13, "name": "Урок 13: Работа с базами данных"},
        {"id": 14, "name": "Урок 14: Веб-разработка"},
        {"id": 15, "name": "Урок 15: Тестирование"},
    ]
    
    async def get_lessons_buttons(cur_page: int, per_page: int):
        """Получает кнопки уроков для текущей страницы"""
        # Вычисляем индексы для текущей страницы
        start_idx = (cur_page - 1) * per_page
        end_idx = start_idx + per_page
        page_lessons = test_lessons[start_idx:end_idx]
        
        # Создаем кнопки для уроков на текущей странице
        buttons = []
        for lesson in page_lessons:
            buttons.append(InlineKeyboardButton(
                text=lesson["name"],
                callback_data=f"lesson_{lesson['id']}"
            ))
        
        return buttons
    
    async def get_lessons_count():
        """Получает общее количество уроков"""
        return len(test_lessons)
    
    async def on_lesson_select(callback, lesson_callback_data: str):
        """Обработчик выбора урока"""
        print(f"Выбран урок: {lesson_callback_data}")
    
    async def on_back(callback):
        """Обработчик кнопки назад"""
        print("Нажата кнопка назад")
    
    # Создаем пагинатор
    paginator = Paginator(
        per_page=5,  # 5 уроков на страницу
        per_row=1,   # По одному уроку в ряду
        lazy_data=get_lessons_buttons,
        lazy_count=get_lessons_count,
        on_select=on_lesson_select,
        on_back=on_back,
        lang="ru"
    )
    
    # Тестируем рендеринг первой страницы
    keyboard = await paginator.render_kb(page=1)
    print("✅ Пагинатор создан успешно!")
    print(f"Количество кнопок на первой странице: {len(keyboard.inline_keyboard)}")
    
    # Выводим структуру клавиатуры
    for i, row in enumerate(keyboard.inline_keyboard):
        print(f"Ряд {i+1}: {[btn.text for btn in row]}")
    
    return paginator

if __name__ == "__main__":
    asyncio.run(test_paginator())
