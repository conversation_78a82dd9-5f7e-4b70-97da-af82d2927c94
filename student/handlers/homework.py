from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext

from common.utils import check_if_id_in_callback_data
from ..keyboards.homework import get_courses_kb, get_subjects_kb, get_lessons_kb
from aiogram.fsm.state import State, StatesGroup
from database import HomeworkRepository, LessonRepository, SubjectRepository, CourseRepository
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button
from aiogramx import Paginator
from aiogramx import Paginator

class HomeworkStates(StatesGroup):
    course = State()
    subject = State()
    lesson = State()
    homework = State()
    confirmation = State()
    test_in_progress = State()
    repeat_test = State()

router = Router()

# Регистрируем пагинатор для уроков
Paginator.register(router)


async def create_lessons_paginator(subject_id: int, course_id: int, course_name: str, subject_name: str):
    """Создает пагинатор для уроков"""

    async def get_lessons_buttons(cur_page: int, per_page: int):
        """Получает кнопки уроков для текущей страницы"""
        # Получаем все уроки
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)

        # Вычисляем индексы для текущей страницы
        start_idx = (cur_page - 1) * per_page
        end_idx = start_idx + per_page
        page_lessons = lessons[start_idx:end_idx]

        # Создаем кнопки для уроков на текущей странице
        buttons = []
        for lesson in page_lessons:
            buttons.append(InlineKeyboardButton(
                text=lesson.name,
                callback_data=f"lesson_{lesson.id}"
            ))

        return buttons

    async def get_lessons_count():
        """Получает общее количество уроков"""
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
        return len(lessons)

    async def on_lesson_select(callback: CallbackQuery, lesson_callback_data: str):
        """Обработчик выбора урока"""
        # Извлекаем lesson_id из callback_data
        lesson_id = int(lesson_callback_data.split("lesson_")[1])

        # Получаем урок из базы данных
        lesson = await LessonRepository.get_by_id(lesson_id)
        if not lesson:
            await callback.answer("❌ Урок не найден", show_alert=True)
            return

        # Получаем домашние задания для этого урока
        homeworks = await HomeworkRepository.get_by_lesson(lesson_id)

        if not homeworks:
            await callback.message.edit_text(
                f"📚 Курс: {course_name}\n"
                f"📖 Предмет: {subject_name}\n"
                f"📝 Урок: {lesson.name}\n\n"
                "❌ Для этого урока пока нет домашних заданий.",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    *get_main_menu_back_button()
                ])
            )
            return

        # Формируем клавиатуру с реальными домашними заданиями
        buttons = []
        for homework in homeworks:
            buttons.append([InlineKeyboardButton(
                text=homework.name,
                callback_data=f"homework_{homework.id}"
            )])

        buttons.extend(get_main_menu_back_button())

        await callback.message.edit_text(
            f"📚 Курс: {course_name}\n"
            f"📖 Предмет: {subject_name}\n"
            f"📝 Урок: {lesson.name}\n\n"
            "Выберите домашнее задание:",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
        )

    async def on_back(callback: CallbackQuery):
        """Обработчик кнопки назад"""
        # Возвращаемся к выбору предмета
        await callback.message.edit_text(
            f"📚 Курс: {course_name}\n\n"
            "Теперь выбери предмет — это поможет выбрать нужные темы и задания:",
            reply_markup=await get_subjects_kb(course_id=course_id, user_id=callback.from_user.id)
        )

    # Создаем пагинатор
    paginator = Paginator(
        per_page=10,  # 10 уроков на страницу
        per_row=1,    # По одному уроку в ряду
        lazy_data=get_lessons_buttons,
        lazy_count=get_lessons_count,
        on_select=on_lesson_select,
        on_back=on_back,
        lang="ru"
    )

    return paginator


@router.callback_query(F.data == "homework")
async def choose_course(callback: CallbackQuery, state: FSMContext):
    await callback.message.edit_text(
        "Выбери курс, по которому хочешь пройти домашнее задание 👇",
        reply_markup=await get_courses_kb(user_id=callback.from_user.id)
    )
    await state.set_state(HomeworkStates.course)

@router.callback_query(HomeworkStates.course, F.data == "no_courses")
async def handle_no_courses(callback: CallbackQuery, state: FSMContext):
    """Обработчик для случая когда у студента нет доступных курсов"""
    await callback.message.edit_text(
        "📚 У вас пока нет доступных курсов\n\n"
        "Обратитесь к администратору или куратору для добавления вас в курсы.",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ])
    )

@router.callback_query(HomeworkStates.course, F.data == "courses_error")
async def handle_courses_error(callback: CallbackQuery, state: FSMContext):
    """Обработчик для случая ошибки загрузки курсов"""
    await callback.message.edit_text(
        "❌ Произошла ошибка при загрузке курсов\n\n"
        "Попробуйте позже или обратитесь к администратору.",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Попробовать снова", callback_data="homework")],
            *get_main_menu_back_button()
        ])
    )

@router.callback_query(HomeworkStates.course, F.data.startswith("course_"))
async def choose_subject(callback: CallbackQuery, state: FSMContext):
    """Выбор предмета для курса"""
    course_id = int(await check_if_id_in_callback_data("course_", callback, state, "course"))

    # Получаем курс из базы данных
    course = await CourseRepository.get_by_id(course_id)
    if not course:
        await callback.answer("❌ Курс не найден", show_alert=True)
        return

    await state.update_data(course_id=course_id, course_name=course.name)

    await callback.message.edit_text(
        f"📚 Курс: {course.name}\n\n"
        "Теперь выбери предмет — это поможет выбрать нужные темы и задания:",
        reply_markup=await get_subjects_kb(course_id=course_id, user_id=callback.from_user.id)
    )
    await state.set_state(HomeworkStates.subject)

@router.callback_query(HomeworkStates.subject, F.data == "no_subjects")
async def handle_no_subjects(callback: CallbackQuery, state: FSMContext):
    """Обработчик для случая когда в курсе нет доступных предметов"""
    user_data = await state.get_data()
    course_name = user_data.get("course_name", "")

    await callback.message.edit_text(
        f"📚 Курс: {course_name}\n\n"
        "📖 В этом курсе нет предметов, доступных для домашних заданий\n\n"
        "Возможно, вы не добавлены в соответствующие группы или предметы не входят в ваши курсы.\n"
        "Обратитесь к администратору или куратору.",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ])
    )

@router.callback_query(HomeworkStates.subject, F.data == "subjects_error")
async def handle_subjects_error(callback: CallbackQuery, state: FSMContext):
    """Обработчик для случая ошибки загрузки предметов"""
    user_data = await state.get_data()
    course_name = user_data.get("course_name", "")
    course_id = user_data.get("course_id")

    await callback.message.edit_text(
        f"📚 Курс: {course_name}\n\n"
        "❌ Произошла ошибка при загрузке предметов\n\n"
        "Попробуйте позже или обратитесь к администратору.",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Попробовать снова", callback_data=f"course_{course_id}")],
            *get_main_menu_back_button()
        ])
    )

@router.callback_query(HomeworkStates.subject, F.data.startswith("subject_"))
async def choose_lesson(callback: CallbackQuery, state: FSMContext):
    """Выбор урока для предмета с пагинацией"""
    subject_id = int(await check_if_id_in_callback_data("subject_", callback, state, "subject"))

    # Получаем предмет из базы данных
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.answer("❌ Предмет не найден", show_alert=True)
        return

    user_data = await state.get_data()
    course_name = user_data.get("course_name", "")
    course_id = user_data.get("course_id")

    await state.update_data(subject_id=subject.id, subject_name=subject.name)

    # Проверяем, есть ли уроки для данного предмета и курса
    lessons = await LessonRepository.get_by_subject_and_course(subject.id, course_id)

    if not lessons:
        await callback.message.edit_text(
            f"📚 Курс: {course_name}\n"
            f"📖 Предмет: {subject.name}\n\n"
            "❌ Для этого предмета пока нет уроков.",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                *get_main_menu_back_button()
            ])
        )
        return

    # Создаем пагинатор для уроков
    paginator = await create_lessons_paginator(
        subject_id=subject.id,
        course_id=course_id,
        course_name=course_name,
        subject_name=subject.name
    )

    # Отображаем первую страницу уроков
    await callback.message.edit_text(
        f"📚 Курс: {course_name}\n"
        f"📖 Предмет: {subject.name}\n\n"
        "Выбери урок, по которому хочешь пройти домашнее задание:",
        reply_markup=await paginator.render_kb(page=1)
    )
    await state.set_state(HomeworkStates.lesson)

@router.callback_query(HomeworkStates.lesson, F.data.startswith("lesson_"))
async def choose_homework(callback: CallbackQuery, state: FSMContext):
    """Выбор домашнего задания для урока (резервный обработчик)"""
    lesson_id = int(await check_if_id_in_callback_data("lesson_", callback, state, "lesson"))

    # Получаем урок из базы данных
    lesson = await LessonRepository.get_by_id(lesson_id)
    if not lesson:
        await callback.answer("❌ Урок не найден", show_alert=True)
        return

    # Получаем домашние задания для этого урока
    homeworks = await HomeworkRepository.get_by_lesson(lesson_id)

    user_data = await state.get_data()
    course_name = user_data.get("course_name", "")
    subject_name = user_data.get("subject_name", "")

    # Сохраняем данные урока в состоянии
    await state.update_data(lesson_id=lesson_id, lesson_name=lesson.name)

    if not homeworks:
        await callback.message.edit_text(
            f"📚 Курс: {course_name}\n"
            f"📖 Предмет: {subject_name}\n"
            f"📝 Урок: {lesson.name}\n\n"
            "❌ Для этого урока пока нет домашних заданий.",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                *get_main_menu_back_button()
            ])
        )
        return

    # Формируем клавиатуру с реальными домашними заданиями
    buttons = []
    for homework in homeworks:
        buttons.append([InlineKeyboardButton(
            text=homework.name,
            callback_data=f"homework_{homework.id}"
        )])

    buttons.extend(get_main_menu_back_button())

    await callback.message.edit_text(
        f"📚 Курс: {course_name}\n"
        f"📖 Предмет: {subject_name}\n"
        f"📝 Урок: {lesson.name}\n\n"
        "Выберите домашнее задание:",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
    )
    await state.set_state(HomeworkStates.homework)

# Обработчики confirm_test, start_quiz, process_answer теперь находятся в homework_quiz.py
